"""
网页爬虫核心模块
处理HTTP请求、异常处理、重试逻辑和反爬策略
"""

import requests
import logging
from typing import Optional, Dict, Any
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
from utils import get_random_headers, random_delay, retry_on_failure


class WebScraper:
    """网页爬虫类"""
    
    def __init__(self, timeout: int = 30, max_retries: int = 3):
        self.timeout = timeout
        self.max_retries = max_retries
        self.session = self._create_session()
    
    def _create_session(self) -> requests.Session:
        """创建带有重试策略的会话"""
        session = requests.Session()
        
        # 设置重试策略
        retry_strategy = Retry(
            total=self.max_retries,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "OPTIONS"]
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        return session
    
    @retry_on_failure(max_retries=3, delay=2.0)
    def fetch_page(self, url: str, **kwargs) -> Optional[str]:
        """获取网页内容"""
        try:
            # 添加随机延迟
            random_delay()
            
            # 准备请求头
            headers = get_random_headers()
            if 'headers' in kwargs:
                headers.update(kwargs['headers'])
            
            # 发送请求
            logging.info(f"正在获取页面: {url}")
            response = self.session.get(
                url,
                headers=headers,
                timeout=self.timeout,
                **{k: v for k, v in kwargs.items() if k != 'headers'}
            )
            
            # 检查响应状态
            response.raise_for_status()
            
            # 检查内容类型
            content_type = response.headers.get('content-type', '').lower()
            if 'text/html' not in content_type and 'application/xhtml' not in content_type:
                logging.warning(f"响应内容类型不是HTML: {content_type}")
            
            # 设置正确的编码
            if response.encoding is None or response.encoding == 'ISO-8859-1':
                response.encoding = response.apparent_encoding or 'utf-8'
            
            logging.info(f"成功获取页面，内容长度: {len(response.text)}")
            return response.text
            
        except requests.exceptions.Timeout:
            logging.error(f"请求超时: {url}")
            raise
        except requests.exceptions.ConnectionError:
            logging.error(f"连接错误: {url}")
            raise
        except requests.exceptions.HTTPError as e:
            logging.error(f"HTTP错误 {e.response.status_code}: {url}")
            if e.response.status_code == 403:
                logging.warning("可能被反爬虫机制阻止，尝试更换User-Agent")
            raise
        except requests.exceptions.RequestException as e:
            logging.error(f"请求异常: {url} - {e}")
            raise
        except Exception as e:
            logging.error(f"未知错误: {url} - {e}")
            raise
    
    def fetch_page_with_retry(self, url: str, max_attempts: int = 3, **kwargs) -> Optional[str]:
        """带重试的页面获取"""
        for attempt in range(max_attempts):
            try:
                return self.fetch_page(url, **kwargs)
            except Exception as e:
                if attempt == max_attempts - 1:
                    logging.error(f"所有重试都失败了: {url}")
                    return None
                else:
                    logging.warning(f"第 {attempt + 1} 次尝试失败: {e}, 等待后重试...")
                    random_delay(2.0, 5.0)  # 更长的延迟
        
        return None
    
    def check_url_accessibility(self, url: str) -> bool:
        """检查URL是否可访问"""
        try:
            response = self.session.head(url, timeout=10, headers=get_random_headers())
            return response.status_code == 200
        except Exception:
            return False
    
    def get_page_info(self, url: str) -> Dict[str, Any]:
        """获取页面基本信息"""
        try:
            response = self.session.head(url, timeout=10, headers=get_random_headers())
            return {
                'status_code': response.status_code,
                'content_type': response.headers.get('content-type', ''),
                'content_length': response.headers.get('content-length', ''),
                'last_modified': response.headers.get('last-modified', ''),
                'accessible': response.status_code == 200
            }
        except Exception as e:
            return {
                'status_code': None,
                'content_type': '',
                'content_length': '',
                'last_modified': '',
                'accessible': False,
                'error': str(e)
            }
    
    def close(self):
        """关闭会话"""
        if self.session:
            self.session.close()
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()


class ScrapingSession:
    """爬虫会话管理器"""
    
    def __init__(self, delay_range: tuple = (1.0, 3.0), max_retries: int = 3):
        self.delay_range = delay_range
        self.max_retries = max_retries
        self.scraper = WebScraper(max_retries=max_retries)
        self.stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'total_items': 0
        }
    
    def fetch_with_stats(self, url: str, **kwargs) -> Optional[str]:
        """带统计的页面获取"""
        self.stats['total_requests'] += 1
        
        try:
            content = self.scraper.fetch_page_with_retry(url, self.max_retries, **kwargs)
            if content:
                self.stats['successful_requests'] += 1
                return content
            else:
                self.stats['failed_requests'] += 1
                return None
        except Exception as e:
            self.stats['failed_requests'] += 1
            logging.error(f"获取页面失败: {url} - {e}")
            return None
    
    def add_items_count(self, count: int):
        """添加爬取到的项目数量"""
        self.stats['total_items'] += count
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        success_rate = 0
        if self.stats['total_requests'] > 0:
            success_rate = (self.stats['successful_requests'] / self.stats['total_requests']) * 100
        
        return {
            **self.stats,
            'success_rate': f"{success_rate:.2f}%"
        }
    
    def print_stats(self):
        """打印统计信息"""
        stats = self.get_stats()
        logging.info("=== 爬取统计信息 ===")
        logging.info(f"总请求数: {stats['total_requests']}")
        logging.info(f"成功请求数: {stats['successful_requests']}")
        logging.info(f"失败请求数: {stats['failed_requests']}")
        logging.info(f"成功率: {stats['success_rate']}")
        logging.info(f"总爬取项目数: {stats['total_items']}")
    
    def close(self):
        """关闭会话"""
        self.scraper.close()
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()
