#!/usr/bin/env python3
"""
测试主程序的脚本 - 只处理一个类别
"""

import os
import pandas as pd
from main import AppleRepairPartsScraper

def test_single_category_main():
    """测试单个类别的完整流程"""
    
    # 创建测试CSV文件，只包含一个类别
    test_data = [{
        '产品类别': 'iPhone 16e维修配件',
        'URL': 'https://www.sunsky-online.com/zh_CN/list/111439/iPhone%2016e%E7%BB%B4%E4%BF%AE%E9%85%8D%E4%BB%B6.htm'
    }]
    
    test_csv_path = 'test_single.csv'
    df = pd.DataFrame(test_data)
    df.to_csv(test_csv_path, index=False, encoding='utf-8-sig')
    
    print(f"创建测试CSV文件: {test_csv_path}")
    
    # 运行爬虫
    scraper = AppleRepairPartsScraper(
        csv_path=test_csv_path,
        output_dir="./test_output",
        template_style="default",
        max_retries=3
    )
    
    success = scraper.run()
    
    # 清理测试文件
    if os.path.exists(test_csv_path):
        os.remove(test_csv_path)
    
    print(f"\n测试完成，成功: {success}")
    
    # 检查生成的文件
    output_file = "./test_output/iPhone 16e维修配件.md"
    if os.path.exists(output_file):
        print(f"\n生成的文件: {output_file}")
        with open(output_file, 'r', encoding='utf-8') as f:
            content = f.read()
            print(f"文件内容长度: {len(content)} 字符")
            print("\n文件内容预览:")
            print(content[:500] + "..." if len(content) > 500 else content)

if __name__ == "__main__":
    test_single_category_main()
