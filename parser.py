"""
HTML解析器模块
使用BeautifulSoup解析HTML内容，提取配件标题和链接
"""

import logging
import re
from typing import List, Dict, Optional, Tuple
from bs4 import BeautifulSoup, Tag
from utils import normalize_url, get_domain_from_url


class HTMLParser:
    """HTML解析器类"""
    
    def __init__(self, base_url: str = ""):
        self.base_url = base_url
        self.domain = get_domain_from_url(base_url)
    
    def parse_html(self, html_content: str) -> BeautifulSoup:
        """解析HTML内容"""
        try:
            soup = BeautifulSoup(html_content, 'lxml')
            return soup
        except Exception as e:
            logging.error(f"HTML解析失败: {e}")
            # 尝试使用html.parser作为备选
            try:
                soup = BeautifulSoup(html_content, 'html.parser')
                return soup
            except Exception as e2:
                logging.error(f"备选解析器也失败: {e2}")
                raise e2
    
    def extract_product_items(self, soup: BeautifulSoup) -> List[Dict[str, str]]:
        """提取产品配件信息"""
        items = []

        # 首先尝试特定的产品信息容器
        product_container = soup.select_one('.productInfoDisplay .shopcart_cont ul')
        if product_container:
            # 获取所有li元素，但排除分隔线
            li_elements = product_container.select('li:not([style*="height:1px"])')
            logging.info(f"在productInfoDisplay容器中找到 {len(li_elements)} 个产品元素")
            items = self._extract_items_from_product_list(li_elements)
            if items:
                return items

        # 如果没找到特定容器，尝试其他选择器
        selectors = [
            '.shopcart_cont ul li:not([style*="height:1px"])',
            '.product-item',
            '.item',
            '.product',
            '.goods-item',
            '.list-item',
            'li[class*="item"]',
            'div[class*="product"]'
        ]

        for selector in selectors:
            elements = soup.select(selector)
            if elements:
                logging.info(f"使用选择器 '{selector}' 找到 {len(elements)} 个元素")
                if selector.startswith('.shopcart_cont'):
                    items = self._extract_items_from_product_list(elements)
                else:
                    items = self._extract_items_from_elements(elements)
                if items:
                    break

        # 如果上述选择器都没找到，尝试通用方法（但应用过滤）
        if not items:
            items = self._extract_items_generic(soup)

        return items

    def _extract_items_from_product_list(self, li_elements: List[Tag]) -> List[Dict[str, str]]:
        """从产品列表的li元素中提取配件信息"""
        items = []

        for li in li_elements:
            try:
                # 查找产品标题链接
                title_link = li.select_one('h3 a')
                if not title_link:
                    continue

                title = title_link.get('title') or title_link.get_text(strip=True)
                url = title_link.get('href', '')

                # 清理和验证数据
                if title and url:
                    title = self._clean_title(title)
                    url = normalize_url(url, self.base_url)

                    # 验证是否是iPhone维修配件且不是导航链接
                    if self._is_iphone_repair_part(title) and not self._is_navigation_link(title, url):
                        items.append({
                            'title': title,
                            'url': url
                        })
                        logging.debug(f"提取到配件: {title}")
                    else:
                        logging.debug(f"跳过非iPhone配件或导航链接: {title}")

            except Exception as e:
                logging.debug(f"提取单个产品失败: {e}")
                continue

        return items

    def _is_iphone_repair_part(self, title: str) -> bool:
        """判断是否是iPhone维修配件"""
        if not title:
            return False

        title_lower = title.lower()

        # iPhone相关关键词
        iphone_keywords = ['iphone', 'ip6p', 'ip6', 'ip5', 'ip4']

        # 维修配件关键词
        repair_keywords = [
            '电池', '屏幕', '摄像头', '排线', '听筒', '扬声器', '充电', '卡托',
            '后盖', '前盖', '玻璃', '液晶', '触摸', '感应', '按键', '开关',
            'battery', 'screen', 'camera', 'cable', 'speaker', 'charging',
            'sim', 'tray', 'back', 'cover', 'glass', 'lcd', 'touch', 'sensor',
            'button', 'switch', '胶', 'adhesive', '工具', 'tool'
        ]

        # 检查是否包含iPhone关键词
        has_iphone = any(keyword in title_lower for keyword in iphone_keywords)

        # 检查是否包含维修配件关键词
        has_repair = any(keyword in title_lower for keyword in repair_keywords)

        return has_iphone and has_repair

    def _is_navigation_link(self, title: str, url: str) -> bool:
        """判断是否是导航链接"""
        if not title or not url:
            return True

        # 导航链接的特征
        navigation_patterns = [
            'javascript:',
            'void(0)',
            '#',
            '/list/',  # 类别列表页面
        ]

        # 导航文本特征
        navigation_texts = [
            'up', '上一页', '下一页', 'next', 'prev',
            '所有类别', 'all categories', 'category',
            'more', 'live chat', '在线聊天', '客服',
            '首页', 'home', '返回', 'back'
        ]

        title_lower = title.lower()
        url_lower = url.lower()

        # 检查URL模式
        for pattern in navigation_patterns:
            if pattern in url_lower:
                return True

        # 检查标题文本
        for text in navigation_texts:
            if text in title_lower:
                return True

        # 检查是否是其他产品类别的链接
        if 'iphone' in title_lower and '维修配件' in title and '/list/' in url:
            return True

        return False

    def _extract_items_from_elements(self, elements: List[Tag]) -> List[Dict[str, str]]:
        """从元素列表中提取配件信息"""
        items = []
        
        for element in elements:
            try:
                item = self._extract_single_item(element)
                if item and item['title'] and item['url']:
                    items.append(item)
            except Exception as e:
                logging.debug(f"提取单个元素失败: {e}")
                continue
        
        return items
    
    def _extract_single_item(self, element: Tag) -> Optional[Dict[str, str]]:
        """从单个元素中提取配件信息"""
        title = ""
        url = ""
        
        # 提取标题
        title_selectors = [
            'a[title]',
            '.title a',
            '.name a',
            '.product-name a',
            'h3 a',
            'h4 a',
            'a'
        ]
        
        for selector in title_selectors:
            title_element = element.select_one(selector)
            if title_element:
                title = title_element.get('title') or title_element.get_text(strip=True)
                url = title_element.get('href', '')
                break
        
        # 如果没找到链接，尝试直接在当前元素中查找
        if not url and element.name == 'a':
            url = element.get('href', '')
            title = title or element.get('title') or element.get_text(strip=True)
        
        # 标准化URL
        if url:
            url = normalize_url(url, self.base_url)
        
        # 清理标题
        if title:
            title = self._clean_title(title)
        
        return {
            'title': title,
            'url': url
        } if title and url else None
    
    def _extract_items_generic(self, soup: BeautifulSoup) -> List[Dict[str, str]]:
        """通用方法提取配件信息"""
        items = []
        
        # 查找所有包含产品链接的a标签
        links = soup.find_all('a', href=True)
        
        for link in links:
            href = link.get('href', '')
            
            # 过滤出可能是产品链接的URL
            if self._is_product_link(href):
                title = link.get('title') or link.get_text(strip=True)
                if title:
                    title = self._clean_title(title)
                    url = normalize_url(href, self.base_url)

                    # 应用iPhone配件和导航链接过滤
                    if title and url and self._is_iphone_repair_part(title) and not self._is_navigation_link(title, url):
                        items.append({
                            'title': title,
                            'url': url
                        })
        
        # 去重
        seen = set()
        unique_items = []
        for item in items:
            key = (item['title'], item['url'])
            if key not in seen:
                seen.add(key)
                unique_items.append(item)
        
        return unique_items
    
    def _is_product_link(self, href: str) -> bool:
        """判断是否是产品链接"""
        if not href:
            return False
        
        # 产品链接的特征
        product_patterns = [
            r'/product/',
            r'/goods/',
            r'/item/',
            r'/p/',
            r'\.htm$',
            r'\.html$'
        ]
        
        # 排除的链接类型
        exclude_patterns = [
            r'javascript:',
            r'mailto:',
            r'tel:',
            r'#',
            r'/category/',
            r'/search/',
            r'/login',
            r'/register'
        ]
        
        # 检查排除模式
        for pattern in exclude_patterns:
            if re.search(pattern, href, re.IGNORECASE):
                return False
        
        # 检查产品模式
        for pattern in product_patterns:
            if re.search(pattern, href, re.IGNORECASE):
                return True
        
        return False
    
    def _clean_title(self, title: str) -> str:
        """清理标题文本"""
        if not title:
            return ""
        
        # 移除多余的空白字符
        title = re.sub(r'\s+', ' ', title.strip())
        
        # 移除特殊字符
        title = re.sub(r'[\r\n\t]', '', title)
        
        # 限制长度
        if len(title) > 200:
            title = title[:200] + "..."
        
        return title
    
    def extract_pagination_info(self, soup: BeautifulSoup) -> Tuple[int, int]:
        """提取分页信息，返回(当前页, 总页数)"""
        current_page = 1
        total_pages = 1
        
        try:
            # 尝试多种分页选择器
            pagination_selectors = [
                '.pagination',
                '.page-nav',
                '.pager',
                '.page-list',
                '[class*="page"]'
            ]
            
            pagination_element = None
            for selector in pagination_selectors:
                pagination_element = soup.select_one(selector)
                if pagination_element:
                    break
            
            if pagination_element:
                # 查找页码链接
                page_links = pagination_element.find_all('a', href=True)
                page_numbers = []
                
                for link in page_links:
                    text = link.get_text(strip=True)
                    if text.isdigit():
                        page_numbers.append(int(text))
                
                if page_numbers:
                    total_pages = max(page_numbers)
                
                # 查找当前页
                current_element = pagination_element.find(['span', 'strong'], class_=lambda x: x and 'current' in x.lower())
                if current_element:
                    current_text = current_element.get_text(strip=True)
                    if current_text.isdigit():
                        current_page = int(current_text)
            
        except Exception as e:
            logging.debug(f"提取分页信息失败: {e}")
        
        return current_page, total_pages
    
    def has_next_page(self, soup: BeautifulSoup) -> bool:
        """检查是否有下一页"""
        try:
            next_selectors = [
                'a[class*="next"]',
                'a[title*="下一页"]',
                'a[title*="Next"]',
                'a:contains("下一页")',
                'a:contains(">")'
            ]
            
            for selector in next_selectors:
                if soup.select_one(selector):
                    return True
            
            return False
        except Exception:
            return False
