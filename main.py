#!/usr/bin/env python3
"""
苹果维修配件爬虫主程序
从CSV文件读取产品类别和URL，爬取配件信息并生成Markdown文件
"""

import argparse
import logging
import os
import sys
from typing import List, Dict, Any

from utils import setup_logging, read_csv_data, save_to_file, ensure_directory_exists
from scraper import ScrapingSession
from parser import HTMLParser
from pagination import PaginationHandler
from markdown_generator import MarkdownGenerator


class AppleRepairPartsScraper:
    """苹果维修配件爬虫主类"""
    
    def __init__(self, csv_path: str, output_dir: str = "./", 
                 template_style: str = "default", max_retries: int = 3):
        self.csv_path = csv_path
        self.output_dir = output_dir
        self.template_style = template_style
        self.max_retries = max_retries
        
        # 初始化组件
        self.markdown_generator = MarkdownGenerator(template_style)
        self.session = None
        
        # 统计信息
        self.stats = {
            'total_categories': 0,
            'successful_categories': 0,
            'failed_categories': 0,
            'total_items': 0
        }
    
    def run(self) -> bool:
        """运行爬虫"""
        try:
            logging.info("开始执行苹果维修配件爬虫...")
            
            # 读取CSV数据
            categories_data = self._load_categories()
            if not categories_data:
                logging.error("没有找到有效的类别数据")
                return False
            
            self.stats['total_categories'] = len(categories_data)
            logging.info(f"共找到 {len(categories_data)} 个产品类别")
            
            # 确保输出目录存在
            ensure_directory_exists(self.output_dir)
            
            # 开始爬取
            with ScrapingSession(max_retries=self.max_retries) as session:
                self.session = session
                
                category_results = []
                
                for i, category_data in enumerate(categories_data, 1):
                    category = category_data.get('产品类别', '')
                    url = category_data.get('URL', '')
                    
                    if not category or not url:
                        logging.warning(f"跳过无效数据: {category_data}")
                        continue
                    
                    logging.info(f"[{i}/{len(categories_data)}] 处理类别: {category}")
                    
                    # 爬取单个类别
                    items = self._scrape_category(category, url)
                    
                    if items:
                        # 生成Markdown文件
                        success = self._save_category_markdown(category, items, url)
                        if success:
                            self.stats['successful_categories'] += 1
                            self.stats['total_items'] += len(items)
                            category_results.append({
                                'category': category,
                                'item_count': len(items),
                                'url': url
                            })
                        else:
                            self.stats['failed_categories'] += 1
                    else:
                        self.stats['failed_categories'] += 1
                        logging.warning(f"类别 {category} 没有爬取到任何配件")
                
                # 生成索引文件
                self._generate_index(category_results)
                
                # 打印统计信息
                self._print_final_stats()
            
            logging.info("爬虫执行完成!")
            return True
            
        except Exception as e:
            logging.error(f"爬虫执行失败: {e}")
            return False
    
    def _load_categories(self) -> List[Dict[str, str]]:
        """加载类别数据"""
        try:
            data = read_csv_data(self.csv_path)
            if not data:
                logging.error(f"无法读取CSV文件: {self.csv_path}")
                return []
            
            # 验证数据格式
            valid_data = []
            for item in data:
                if '产品类别' in item and 'URL' in item:
                    valid_data.append(item)
                else:
                    logging.warning(f"跳过格式不正确的数据: {item}")
            
            return valid_data
            
        except Exception as e:
            logging.error(f"加载类别数据失败: {e}")
            return []
    
    def _scrape_category(self, category: str, url: str) -> List[Dict[str, str]]:
        """爬取单个类别的配件信息"""
        try:
            logging.info(f"开始爬取类别: {category}")
            
            # 获取第一页内容
            html_content = self.session.fetch_with_stats(url)
            if not html_content:
                logging.error(f"无法获取页面内容: {url}")
                return []
            
            # 解析HTML
            parser = HTMLParser(url)
            soup = parser.parse_html(html_content)
            
            # 提取配件信息
            items = parser.extract_product_items(soup)
            logging.info(f"第1页找到 {len(items)} 个配件")
            
            # 检查分页
            pagination_handler = PaginationHandler(url)
            current_page, total_pages = pagination_handler.detect_pagination_info(soup)
            
            if total_pages > 1:
                logging.info(f"检测到分页，总共 {total_pages} 页")
                
                # 爬取其他页面
                for page in range(2, total_pages + 1):
                    try:
                        page_url = pagination_handler.build_page_url(page)
                        logging.info(f"爬取第 {page} 页: {page_url}")
                        
                        page_html = self.session.fetch_with_stats(page_url)
                        if page_html:
                            page_soup = parser.parse_html(page_html)
                            page_items = parser.extract_product_items(page_soup)
                            items.extend(page_items)
                            logging.info(f"第{page}页找到 {len(page_items)} 个配件")
                        else:
                            logging.warning(f"无法获取第 {page} 页内容")
                            
                    except Exception as e:
                        logging.error(f"爬取第 {page} 页失败: {e}")
                        continue
            
            # 去重
            unique_items = self._deduplicate_items(items)
            logging.info(f"类别 {category} 总共找到 {len(unique_items)} 个唯一配件")

            self.session.add_items_count(len(unique_items))
            return unique_items
            
        except Exception as e:
            logging.error(f"爬取类别 {category} 失败: {e}")
            return []
    
    def _deduplicate_items(self, items: List[Dict[str, str]]) -> List[Dict[str, str]]:
        """去除重复的配件"""
        seen = set()
        unique_items = []
        
        for item in items:
            # 使用标题和URL作为唯一标识
            key = (item.get('title', '').strip(), item.get('url', '').strip())
            if key not in seen and key[0] and key[1]:
                seen.add(key)
                unique_items.append(item)
        
        return unique_items
    
    def _save_category_markdown(self, category: str, items: List[Dict[str, str]], 
                               source_url: str) -> bool:
        """保存类别的Markdown文件"""
        try:
            # 生成Markdown内容
            additional_info = {
                '爬取页面数': self._estimate_pages_scraped(source_url),
                '数据完整性': '完整' if items else '无数据'
            }
            
            markdown_content = self.markdown_generator.generate_markdown(
                category, items, source_url, additional_info
            )
            
            # 验证内容
            if not self.markdown_generator.validate_markdown(markdown_content):
                logging.warning(f"Markdown内容验证失败: {category}")
            
            # 生成文件名并保存
            filename = self.markdown_generator.generate_filename(category)
            filepath = os.path.join(self.output_dir, filename)
            
            success = save_to_file(markdown_content, filepath)
            if success:
                logging.info(f"成功保存文件: {filepath}")
            
            return success
            
        except Exception as e:
            logging.error(f"保存类别 {category} 的Markdown文件失败: {e}")
            return False
    
    def _estimate_pages_scraped(self, url: str) -> int:
        """估算爬取的页面数（简单实现）"""
        # 这里可以根据实际情况改进
        return 1
    
    def _generate_index(self, category_results: List[Dict[str, Any]]):
        """生成索引文件"""
        try:
            index_content = self.markdown_generator.generate_index_markdown(
                category_results, self.output_dir
            )
            
            index_path = os.path.join(self.output_dir, "README.md")
            save_to_file(index_content, index_path)
            logging.info(f"生成索引文件: {index_path}")
            
        except Exception as e:
            logging.error(f"生成索引文件失败: {e}")
    
    def _print_final_stats(self):
        """打印最终统计信息"""
        logging.info("=== 最终统计信息 ===")
        logging.info(f"总类别数: {self.stats['total_categories']}")
        logging.info(f"成功处理类别数: {self.stats['successful_categories']}")
        logging.info(f"失败类别数: {self.stats['failed_categories']}")
        logging.info(f"总配件数: {self.stats['total_items']}")
        
        if self.stats['total_categories'] > 0:
            success_rate = (self.stats['successful_categories'] / self.stats['total_categories']) * 100
            logging.info(f"成功率: {success_rate:.2f}%")
        
        # 打印会话统计
        if self.session:
            self.session.print_stats()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='苹果维修配件爬虫')
    parser.add_argument('--csv_path', default='苹果维修配件分类链接表.csv',
                       help='CSV文件路径 (默认: 苹果维修配件分类链接表.csv)')
    parser.add_argument('--output_dir', default='./',
                       help='输出目录 (默认: ./)')
    parser.add_argument('--template', choices=['default', 'detailed', 'simple', 'table'],
                       default='default', help='Markdown模板样式 (默认: default)')
    parser.add_argument('--max_retries', type=int, default=3,
                       help='最大重试次数 (默认: 3)')
    parser.add_argument('--log_level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       default='INFO', help='日志级别 (默认: INFO)')
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging(args.log_level)
    
    # 检查CSV文件是否存在
    if not os.path.exists(args.csv_path):
        logging.error(f"CSV文件不存在: {args.csv_path}")
        sys.exit(1)
    
    # 创建爬虫实例并运行
    scraper = AppleRepairPartsScraper(
        csv_path=args.csv_path,
        output_dir=args.output_dir,
        template_style=args.template,
        max_retries=args.max_retries
    )
    
    success = scraper.run()
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
