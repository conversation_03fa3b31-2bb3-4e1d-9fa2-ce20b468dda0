# 苹果维修配件爬虫项目设计方案

## 项目概述

本项目旨在开发一个爬虫程序，从CSV文件中读取苹果产品类别及其对应的配件汇总页面URL，自动爬取每个类别下所有配件的标题和链接，并将这些信息以Markdown格式保存到相应的文件中。

## 技术栈

- **编程语言**: Python 3.x
- **主要库**:
  - requests: 发送HTTP请求获取网页内容
  - BeautifulSoup4: 解析HTML内容
  - pandas: 处理CSV文件
  - time & random: 添加随机延迟
  - os/pathlib: 文件和目录操作

## 项目文件结构

```
apple_repair_parts_scraper/
├── main.py                  # 主程序入口
├── scraper.py               # 网页爬虫核心逻辑
├── pagination.py            # 分页处理模块
├── parser.py                # HTML解析器
├── markdown_generator.py    # Markdown生成器
├── utils.py                 # 工具函数
└── requirements.txt         # 项目依赖
```

## 功能模块详细设计

### 1. CSV解析模块

- 读取CSV文件，提取产品类别和URL
- 将数据转换为字典列表，便于后续处理

### 2. 网页爬取模块

- 发送HTTP请求获取网页内容
- 处理网络异常和重试逻辑
- 添加随机延迟避免被反爬
- 支持自定义请求头和代理

### 3. 分页处理模块

- 从页面中提取总页数信息
- 构建各页面的URL
- 循环爬取所有页面的内容

### 4. HTML解析模块

- 使用BeautifulSoup解析HTML内容
- 提取配件标题和链接
- 处理特殊字符和编码问题

### 5. Markdown生成模块

- 将配件信息转换为Markdown格式
- 支持自定义Markdown模板

### 6. 文件保存模块

- 将Markdown内容保存到指定目录
- 使用产品类别作为文件名

## 程序流程

```mermaid
graph TD
    A[读取CSV文件] --> B[遍历每个产品类别]
    B --> C[发送HTTP请求获取第一页内容]
    C --> D[解析HTML提取配件信息]
    D --> E[检测是否有分页]
    E -->|是| F[提取总页数]
    F --> G[遍历剩余页面]
    G --> H[发送HTTP请求获取下一页]
    H --> I[解析HTML提取配件信息]
    I --> J[检查是否还有下一页]
    J -->|是| H
    J -->|否| K[合并所有页面数据]
    E -->|否| K
    K --> L[将配件信息转换为Markdown格式]
    L --> M[保存到对应的Markdown文件]
    M --> N[检查是否有下一个类别]
    N -->|是| B
    N -->|否| O[完成]
```

## 输出格式

每个产品类别的Markdown文件格式如下：

```markdown
# [产品类别名称]

## 配件列表

- [配件1名称](配件1链接)
- [配件2名称](配件2链接)
...
```

## 反爬策略

1. **添加随机延迟**: 每次请求之间添加1-3秒的随机延迟
2. **使用随机User-Agent**: 每次请求随机更换User-Agent
3. **错误处理与重试**: 当请求失败时，添加重试机制，最多重试3次
4. **请求头模拟**: 添加Referer等请求头，模拟真实浏览器行为

## 异常处理

1. **网络异常**: 处理连接超时、连接错误等异常
2. **解析异常**: 处理HTML解析失败的情况
3. **文件操作异常**: 处理文件读写过程中的异常

## 扩展功能

1. **多线程支持**: 可选择启用多线程加速爬取过程
2. **断点续爬**: 支持从中断处继续爬取
3. **日志记录**: 记录爬取过程中的关键信息和异常
4. **配置文件**: 支持通过配置文件自定义爬虫行为

## 使用方法

```
python main.py --csv_path "苹果维修配件分类链接表.csv" --output_dir "./"
```

## 执行效果

程序执行后，将在指定目录下生成与产品类别对应的Markdown文件，每个文件包含该类别下所有配件的标题和链接。
