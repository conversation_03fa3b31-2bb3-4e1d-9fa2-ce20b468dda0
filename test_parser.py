#!/usr/bin/env python3
"""
测试解析器的脚本
"""

import logging
from utils import setup_logging
from scraper import WebScraper
from parser import HTMLParser

def test_single_category():
    """测试单个类别的解析"""
    setup_logging("DEBUG")
    
    # 测试iPhone 16e维修配件
    test_url = "https://www.sunsky-online.com/zh_CN/list/111439/iPhone%2016e%E7%BB%B4%E4%BF%AE%E9%85%8D%E4%BB%B6.htm"
    
    print(f"测试URL: {test_url}")
    
    # 获取页面内容
    with WebScraper() as scraper:
        html_content = scraper.fetch_page(test_url)
        
        if not html_content:
            print("无法获取页面内容")
            return
        
        print(f"页面内容长度: {len(html_content)}")
        
        # 解析HTML
        parser = HTMLParser(test_url)
        soup = parser.parse_html(html_content)
        
        # 提取配件信息
        items = parser.extract_product_items(soup)
        
        print(f"\n找到 {len(items)} 个配件:")
        for i, item in enumerate(items, 1):
            print(f"{i}. {item['title']}")
            print(f"   URL: {item['url']}")
            print()

if __name__ == "__main__":
    test_single_category()
