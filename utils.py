"""
工具函数模块
包含随机延迟、User-Agent生成、文件操作等通用工具函数
"""

import time
import random
import os
import logging
from pathlib import Path
from typing import List, Dict, Any


def setup_logging(log_level: str = "INFO") -> None:
    """设置日志配置"""
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('scraper.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )


def get_random_user_agent() -> str:
    """获取随机User-Agent"""
    user_agents = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    ]
    return random.choice(user_agents)


def get_random_headers() -> Dict[str, str]:
    """获取随机请求头"""
    return {
        'User-Agent': get_random_user_agent(),
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Cache-Control': 'max-age=0'
    }


def random_delay(min_seconds: float = 1.0, max_seconds: float = 3.0) -> None:
    """添加随机延迟"""
    delay = random.uniform(min_seconds, max_seconds)
    logging.info(f"等待 {delay:.2f} 秒...")
    time.sleep(delay)


def ensure_directory_exists(directory: str) -> None:
    """确保目录存在，如果不存在则创建"""
    Path(directory).mkdir(parents=True, exist_ok=True)


def sanitize_filename(filename: str) -> str:
    """清理文件名，移除不合法字符"""
    invalid_chars = '<>:"/\\|?*'
    for char in invalid_chars:
        filename = filename.replace(char, '_')
    return filename.strip()


def save_to_file(content: str, filepath: str, encoding: str = 'utf-8') -> bool:
    """保存内容到文件"""
    try:
        # 确保目录存在
        directory = os.path.dirname(filepath)
        if directory:
            ensure_directory_exists(directory)
        
        with open(filepath, 'w', encoding=encoding) as f:
            f.write(content)
        logging.info(f"文件保存成功: {filepath}")
        return True
    except Exception as e:
        logging.error(f"保存文件失败 {filepath}: {e}")
        return False


def read_csv_data(csv_path: str) -> List[Dict[str, str]]:
    """读取CSV文件数据"""
    try:
        import pandas as pd
        df = pd.read_csv(csv_path)
        return df.to_dict('records')
    except Exception as e:
        logging.error(f"读取CSV文件失败 {csv_path}: {e}")
        return []


def format_url(base_url: str, page: int = 1) -> str:
    """格式化URL，添加分页参数"""
    if '?' in base_url:
        return f"{base_url}&page={page}"
    else:
        return f"{base_url}?page={page}"


def is_valid_url(url: str) -> bool:
    """检查URL是否有效"""
    return url and (url.startswith('http://') or url.startswith('https://'))


def retry_on_failure(max_retries: int = 3, delay: float = 1.0):
    """重试装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            last_exception = None
            for attempt in range(max_retries):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    if attempt < max_retries - 1:
                        logging.warning(f"第 {attempt + 1} 次尝试失败: {e}, 重试中...")
                        time.sleep(delay * (attempt + 1))  # 递增延迟
                    else:
                        logging.error(f"所有重试都失败了: {e}")
            raise last_exception
        return wrapper
    return decorator


def get_domain_from_url(url: str) -> str:
    """从URL中提取域名"""
    try:
        from urllib.parse import urlparse
        parsed = urlparse(url)
        return f"{parsed.scheme}://{parsed.netloc}"
    except Exception:
        return ""


def normalize_url(url: str, base_url: str = "") -> str:
    """标准化URL，处理相对路径"""
    if url.startswith('http'):
        return url
    elif url.startswith('//'):
        return f"https:{url}"
    elif url.startswith('/'):
        domain = get_domain_from_url(base_url)
        return f"{domain}{url}"
    else:
        return url
