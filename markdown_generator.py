"""
Markdown生成器模块
将配件信息转换为指定格式的Markdown内容
"""

import logging
from datetime import datetime
from typing import List, Dict, Optional
from utils import sanitize_filename


class MarkdownGenerator:
    """Markdown生成器类"""
    
    def __init__(self, template_style: str = "default"):
        self.template_style = template_style
        self.templates = {
            "default": self._default_template,
            "detailed": self._detailed_template,
            "simple": self._simple_template,
            "table": self._table_template
        }
    
    def generate_markdown(self, category: str, items: List[Dict[str, str]], 
                         source_url: str = "", additional_info: Dict = None) -> str:
        """生成Markdown内容"""
        try:
            template_func = self.templates.get(self.template_style, self._default_template)
            return template_func(category, items, source_url, additional_info or {})
        except Exception as e:
            logging.error(f"生成Markdown失败: {e}")
            return self._fallback_template(category, items, source_url)
    
    def _default_template(self, category: str, items: List[Dict[str, str]], 
                         source_url: str, additional_info: Dict) -> str:
        """默认模板"""
        markdown_content = []
        
        # 标题
        markdown_content.append(f"# {category}")
        markdown_content.append("")
        
        # 基本信息
        if source_url:
            markdown_content.append(f"**数据源**: [{source_url}]({source_url})")
            markdown_content.append("")
        
        # 统计信息
        markdown_content.append(f"**配件总数**: {len(items)}")
        markdown_content.append(f"**更新时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        markdown_content.append("")
        
        # 配件列表
        markdown_content.append("## 配件列表")
        markdown_content.append("")
        
        if items:
            for i, item in enumerate(items, 1):
                title = item.get('title', '').strip()
                url = item.get('url', '').strip()
                
                if title and url:
                    # 清理标题中的特殊字符
                    clean_title = self._clean_markdown_text(title)
                    markdown_content.append(f"{i}. [{clean_title}]({url})")
                elif title:
                    markdown_content.append(f"{i}. {self._clean_markdown_text(title)}")
        else:
            markdown_content.append("暂无配件信息")
        
        markdown_content.append("")
        
        # 附加信息
        if additional_info:
            markdown_content.append("## 附加信息")
            markdown_content.append("")
            for key, value in additional_info.items():
                markdown_content.append(f"- **{key}**: {value}")
            markdown_content.append("")
        
        return "\n".join(markdown_content)
    
    def _detailed_template(self, category: str, items: List[Dict[str, str]], 
                          source_url: str, additional_info: Dict) -> str:
        """详细模板"""
        markdown_content = []
        
        # 标题和描述
        markdown_content.append(f"# {category} - 详细信息")
        markdown_content.append("")
        markdown_content.append(f"本页面包含 **{category}** 的所有维修配件信息。")
        markdown_content.append("")
        
        # 元数据
        markdown_content.append("## 页面信息")
        markdown_content.append("")
        markdown_content.append(f"| 项目 | 信息 |")
        markdown_content.append(f"|------|------|")
        markdown_content.append(f"| 数据源 | [{source_url}]({source_url}) |")
        markdown_content.append(f"| 配件总数 | {len(items)} |")
        markdown_content.append(f"| 更新时间 | {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} |")
        
        # 添加附加信息到表格
        if additional_info:
            for key, value in additional_info.items():
                markdown_content.append(f"| {key} | {value} |")
        
        markdown_content.append("")
        
        # 配件列表
        markdown_content.append("## 配件详细列表")
        markdown_content.append("")
        
        if items:
            for i, item in enumerate(items, 1):
                title = item.get('title', '').strip()
                url = item.get('url', '').strip()
                
                if title and url:
                    clean_title = self._clean_markdown_text(title)
                    markdown_content.append(f"### {i}. {clean_title}")
                    markdown_content.append("")
                    markdown_content.append(f"**链接**: [{url}]({url})")
                    markdown_content.append("")
        else:
            markdown_content.append("暂无配件信息")
        
        return "\n".join(markdown_content)
    
    def _simple_template(self, category: str, items: List[Dict[str, str]], 
                        source_url: str, additional_info: Dict) -> str:
        """简单模板"""
        markdown_content = []
        
        markdown_content.append(f"# {category}")
        markdown_content.append("")
        
        if items:
            for item in items:
                title = item.get('title', '').strip()
                url = item.get('url', '').strip()
                
                if title and url:
                    clean_title = self._clean_markdown_text(title)
                    markdown_content.append(f"- [{clean_title}]({url})")
                elif title:
                    markdown_content.append(f"- {self._clean_markdown_text(title)}")
        else:
            markdown_content.append("暂无配件信息")
        
        return "\n".join(markdown_content)
    
    def _table_template(self, category: str, items: List[Dict[str, str]], 
                       source_url: str, additional_info: Dict) -> str:
        """表格模板"""
        markdown_content = []
        
        markdown_content.append(f"# {category}")
        markdown_content.append("")
        
        if source_url:
            markdown_content.append(f"**数据源**: [{source_url}]({source_url})")
            markdown_content.append("")
        
        markdown_content.append(f"**配件总数**: {len(items)}")
        markdown_content.append(f"**更新时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        markdown_content.append("")
        
        # 表格头
        markdown_content.append("| 序号 | 配件名称 | 链接 |")
        markdown_content.append("|------|----------|------|")
        
        if items:
            for i, item in enumerate(items, 1):
                title = item.get('title', '').strip()
                url = item.get('url', '').strip()
                
                clean_title = self._clean_markdown_text(title) if title else "无标题"
                link_text = f"[查看详情]({url})" if url else "无链接"
                
                markdown_content.append(f"| {i} | {clean_title} | {link_text} |")
        else:
            markdown_content.append("| - | 暂无配件信息 | - |")
        
        return "\n".join(markdown_content)
    
    def _fallback_template(self, category: str, items: List[Dict[str, str]], source_url: str) -> str:
        """备用模板"""
        markdown_content = []
        
        markdown_content.append(f"# {category}")
        markdown_content.append("")
        markdown_content.append("## 配件列表")
        markdown_content.append("")
        
        if items:
            for item in items:
                title = item.get('title', '').strip()
                url = item.get('url', '').strip()
                
                if title and url:
                    markdown_content.append(f"- [{title}]({url})")
                elif title:
                    markdown_content.append(f"- {title}")
        else:
            markdown_content.append("暂无配件信息")
        
        return "\n".join(markdown_content)
    
    def _clean_markdown_text(self, text: str) -> str:
        """清理Markdown文本中的特殊字符"""
        if not text:
            return ""
        
        # 替换Markdown特殊字符
        replacements = {
            '[': '\\[',
            ']': '\\]',
            '(': '\\(',
            ')': '\\)',
            '*': '\\*',
            '_': '\\_',
            '`': '\\`',
            '#': '\\#',
            '|': '\\|'
        }
        
        for char, replacement in replacements.items():
            text = text.replace(char, replacement)
        
        return text.strip()
    
    def generate_filename(self, category: str, extension: str = ".md") -> str:
        """生成文件名"""
        clean_name = sanitize_filename(category)
        return f"{clean_name}{extension}"
    
    def generate_index_markdown(self, categories: List[Dict[str, str]], 
                              output_dir: str = "./") -> str:
        """生成索引Markdown文件"""
        markdown_content = []
        
        markdown_content.append("# 苹果维修配件索引")
        markdown_content.append("")
        markdown_content.append(f"**总类别数**: {len(categories)}")
        markdown_content.append(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        markdown_content.append("")
        
        markdown_content.append("## 产品类别")
        markdown_content.append("")
        
        for category_info in categories:
            category = category_info.get('category', '')
            filename = self.generate_filename(category)
            item_count = category_info.get('item_count', 0)
            
            if category:
                markdown_content.append(f"- [{category}]({filename}) ({item_count} 个配件)")
        
        markdown_content.append("")
        markdown_content.append("---")
        markdown_content.append("*此索引由苹果维修配件爬虫自动生成*")
        
        return "\n".join(markdown_content)
    
    def validate_markdown(self, content: str) -> bool:
        """验证Markdown内容"""
        try:
            # 基本验证
            if not content or not content.strip():
                return False
            
            # 检查是否包含标题
            if not content.startswith('#'):
                logging.warning("Markdown内容缺少标题")
            
            # 检查链接格式
            import re
            link_pattern = r'\[([^\]]+)\]\(([^)]+)\)'
            links = re.findall(link_pattern, content)
            
            for link_text, link_url in links:
                if not link_url.strip():
                    logging.warning(f"发现空链接: {link_text}")
            
            return True
            
        except Exception as e:
            logging.error(f"验证Markdown内容失败: {e}")
            return False
