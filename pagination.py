"""
分页处理模块
检测分页信息，构建页面URL，处理多页面爬取
"""

import logging
import re
from typing import List, Optional, Tuple, Generator
from urllib.parse import urlparse, parse_qs, urlencode, urlunparse
from bs4 import BeautifulSoup


class PaginationHandler:
    """分页处理器"""
    
    def __init__(self, base_url: str):
        self.base_url = base_url
        self.parsed_url = urlparse(base_url)
    
    def detect_pagination_info(self, soup: BeautifulSoup) -> Tuple[int, int]:
        """检测分页信息，返回(当前页, 总页数)"""
        current_page = 1
        total_pages = 1
        
        try:
            # 方法1: 查找分页容器
            pagination_info = self._find_pagination_container(soup)
            if pagination_info[1] > 1:
                return pagination_info
            
            # 方法2: 查找页码链接
            pagination_info = self._find_page_links(soup)
            if pagination_info[1] > 1:
                return pagination_info
            
            # 方法3: 查找分页文本信息
            pagination_info = self._find_pagination_text(soup)
            if pagination_info[1] > 1:
                return pagination_info
            
        except Exception as e:
            logging.debug(f"检测分页信息失败: {e}")
        
        return current_page, total_pages
    
    def _find_pagination_container(self, soup: BeautifulSoup) -> Tuple[int, int]:
        """在分页容器中查找分页信息"""
        current_page = 1
        total_pages = 1
        
        # 常见的分页容器选择器
        pagination_selectors = [
            '.pagination',
            '.page-nav',
            '.pager',
            '.page-list',
            '.page-info',
            '[class*="page"]',
            '[id*="page"]'
        ]
        
        for selector in pagination_selectors:
            container = soup.select_one(selector)
            if container:
                # 查找当前页
                current_elements = container.select('[class*="current"], [class*="active"], .on, strong')
                for elem in current_elements:
                    text = elem.get_text(strip=True)
                    if text.isdigit():
                        current_page = int(text)
                        break
                
                # 查找所有页码
                page_links = container.find_all('a', href=True)
                page_numbers = []
                
                for link in page_links:
                    text = link.get_text(strip=True)
                    if text.isdigit():
                        page_numbers.append(int(text))
                    
                    # 也检查href中的页码
                    href = link.get('href', '')
                    page_num = self._extract_page_from_url(href)
                    if page_num:
                        page_numbers.append(page_num)
                
                if page_numbers:
                    total_pages = max(page_numbers)
                    break
        
        return current_page, total_pages
    
    def _find_page_links(self, soup: BeautifulSoup) -> Tuple[int, int]:
        """通过页码链接查找分页信息"""
        current_page = 1
        total_pages = 1
        
        # 查找所有可能的页码链接
        all_links = soup.find_all('a', href=True)
        page_numbers = []
        
        for link in all_links:
            href = link.get('href', '')
            text = link.get_text(strip=True)
            
            # 从链接文本中提取页码
            if text.isdigit():
                page_numbers.append(int(text))
            
            # 从URL中提取页码
            page_num = self._extract_page_from_url(href)
            if page_num:
                page_numbers.append(page_num)
        
        if page_numbers:
            total_pages = max(page_numbers)
        
        return current_page, total_pages
    
    def _find_pagination_text(self, soup: BeautifulSoup) -> Tuple[int, int]:
        """通过分页文本信息查找分页信息"""
        current_page = 1
        total_pages = 1
        
        # 查找包含分页信息的文本
        text_patterns = [
            r'第\s*(\d+)\s*页.*共\s*(\d+)\s*页',
            r'页次：(\d+)/(\d+)',
            r'(\d+)\s*/\s*(\d+)',
            r'Page\s+(\d+)\s+of\s+(\d+)',
            r'共(\d+)页.*第(\d+)页'
        ]
        
        page_text = soup.get_text()
        
        for pattern in text_patterns:
            matches = re.findall(pattern, page_text, re.IGNORECASE)
            if matches:
                match = matches[0]
                if len(match) == 2:
                    try:
                        current_page = int(match[0])
                        total_pages = int(match[1])
                        break
                    except ValueError:
                        continue
        
        return current_page, total_pages
    
    def _extract_page_from_url(self, url: str) -> Optional[int]:
        """从URL中提取页码"""
        try:
            # 常见的页码参数名
            page_params = ['page', 'p', 'pagenum', 'pageindex', 'pn']
            
            parsed = urlparse(url)
            query_params = parse_qs(parsed.query)
            
            for param in page_params:
                if param in query_params:
                    page_value = query_params[param][0]
                    if page_value.isdigit():
                        return int(page_value)
            
            # 从路径中提取页码
            path_match = re.search(r'/page/(\d+)', parsed.path)
            if path_match:
                return int(path_match.group(1))
            
            # 从文件名中提取页码
            filename_match = re.search(r'_(\d+)\.htm', parsed.path)
            if filename_match:
                return int(filename_match.group(1))
            
        except Exception:
            pass
        
        return None
    
    def generate_page_urls(self, total_pages: int, start_page: int = 1) -> Generator[str, None, None]:
        """生成所有页面的URL"""
        for page in range(start_page, total_pages + 1):
            yield self.build_page_url(page)
    
    def build_page_url(self, page_number: int) -> str:
        """构建指定页码的URL"""
        if page_number == 1:
            return self.base_url
        
        # 尝试不同的URL构建方式
        url_builders = [
            self._build_url_with_query_param,
            self._build_url_with_path,
            self._build_url_with_fragment
        ]
        
        for builder in url_builders:
            try:
                url = builder(page_number)
                if url != self.base_url:
                    return url
            except Exception:
                continue
        
        # 默认使用查询参数
        return self._build_url_with_query_param(page_number, 'page')
    
    def _build_url_with_query_param(self, page_number: int, param_name: str = 'page') -> str:
        """使用查询参数构建URL"""
        query_params = parse_qs(self.parsed_url.query)
        query_params[param_name] = [str(page_number)]
        
        new_query = urlencode(query_params, doseq=True)
        new_url = urlunparse((
            self.parsed_url.scheme,
            self.parsed_url.netloc,
            self.parsed_url.path,
            self.parsed_url.params,
            new_query,
            self.parsed_url.fragment
        ))
        
        return new_url
    
    def _build_url_with_path(self, page_number: int) -> str:
        """使用路径构建URL"""
        path = self.parsed_url.path
        
        # 如果路径已经包含页码，替换它
        if re.search(r'/page/\d+', path):
            new_path = re.sub(r'/page/\d+', f'/page/{page_number}', path)
        else:
            # 添加页码到路径
            if path.endswith('/'):
                new_path = f"{path}page/{page_number}"
            else:
                new_path = f"{path}/page/{page_number}"
        
        new_url = urlunparse((
            self.parsed_url.scheme,
            self.parsed_url.netloc,
            new_path,
            self.parsed_url.params,
            self.parsed_url.query,
            self.parsed_url.fragment
        ))
        
        return new_url
    
    def _build_url_with_fragment(self, page_number: int) -> str:
        """使用片段构建URL"""
        new_fragment = f"page={page_number}"
        
        new_url = urlunparse((
            self.parsed_url.scheme,
            self.parsed_url.netloc,
            self.parsed_url.path,
            self.parsed_url.params,
            self.parsed_url.query,
            new_fragment
        ))
        
        return new_url
    
    def has_next_page(self, soup: BeautifulSoup, current_page: int, total_pages: int) -> bool:
        """检查是否有下一页"""
        if current_page < total_pages:
            return True
        
        # 检查是否有"下一页"链接
        next_selectors = [
            'a[class*="next"]',
            'a[title*="下一页"]',
            'a[title*="Next"]',
            'a:contains("下一页")',
            'a:contains(">")',
            'a:contains("Next")'
        ]
        
        for selector in next_selectors:
            if soup.select_one(selector):
                return True
        
        return False
