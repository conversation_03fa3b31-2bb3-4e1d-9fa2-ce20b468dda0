#!/usr/bin/env python3
"""
测试分页功能的脚本
"""

import pandas as pd
from main import AppleRepairPartsScraper

def test_pagination_url():
    """测试需要翻页的URL"""
    
    # 创建测试CSV文件，使用用户提供的URL
    test_data = [{
        '产品类别': '苹果配件搜索结果',
        'URL': 'https://www.sunsky-online.com/zh_CN/product/default!search.do?keyword=&categoryId=110753&priceRange=&brandIds=&colorIds=&certs=&brandModelIds=&propOptions=&closedFilters=&orderBy=rank&desc=true'
    }]
    
    test_csv_path = 'test_pagination.csv'
    df = pd.DataFrame(test_data)
    df.to_csv(test_csv_path, index=False, encoding='utf-8-sig')
    
    print(f"创建测试CSV文件: {test_csv_path}")
    print(f"测试URL: {test_data[0]['URL']}")
    
    # 运行爬虫
    scraper = AppleRepairPartsScraper(
        csv_path=test_csv_path,
        output_dir="./pagination_output",
        template_style="default",
        max_retries=3
    )
    
    success = scraper.run()
    
    # 清理测试文件
    import os
    if os.path.exists(test_csv_path):
        os.remove(test_csv_path)
    
    print(f"\n测试完成，成功: {success}")
    
    # 检查生成的文件
    output_file = "./pagination_output/苹果配件搜索结果.md"
    if os.path.exists(output_file):
        print(f"\n生成的文件: {output_file}")
        with open(output_file, 'r', encoding='utf-8') as f:
            content = f.read()
            print(f"文件内容长度: {len(content)} 字符")
            
            # 统计配件数量
            lines = content.split('\n')
            item_count = 0
            for line in lines:
                if line.strip().startswith(('1.', '2.', '3.', '4.', '5.', '6.', '7.', '8.', '9.')) or \
                   (line.strip().startswith(tuple(f"{i}." for i in range(10, 1000)))):
                    item_count += 1
            
            print(f"实际配件数量: {item_count}")
            print("\n文件内容预览:")
            print(content[:1000] + "..." if len(content) > 1000 else content)

if __name__ == "__main__":
    test_pagination_url()
